//
//  ImageProcessViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/22.
//

import SwiftUI
import PhotosUI
import Vision
import CoreImage
import Photos

@MainActor
class ImageProcessViewModel: ObservableObject {
    @Published var selectedPhoto: PhotosPickerItem?
    @Published var inputImage: UIImage?
    @Published var processedImage: UIImage?
    @Published var subjectObservation: VNInstanceMaskObservation?
    @Published var subjects: [ImageProcess.SubjectInfo] = [] // 所有检测到的主体
    @Published var selectedSubjectIndices: Set<Int> = [] // 选中的主体索引
    @Published var showPhotoPicker = false
    @Published var showSaveAlert = false
    @Published var isProcessing = false
    @Published var showPermissionAlert = false
    @Published var showSubjectSelection = false // 显示主体选择状态

    // MARK: - 卡片类型选择相关属性
    @Published var selectedCardType: CardType? // 选择的卡片类型
    @Published var showCardTypeSelection = true // 显示卡片类型选择
    @Published var extractedThemeColor: String? // 提取的主题色

    // MARK: - 流程控制相关属性
    @Published var currentStep: ProcessStep = .cardTypeSelection // 当前步骤

    enum ProcessStep {
        case cardTypeSelection  // 卡片类型选择
        case imageSelection     // 图片选择
        case subjectSelection   // 主体选择（仅购物卡片）
        case extractionResult   // 主体提取结果显示
        case imageAnalysis      // 图像分析和确认
    }

    // MARK: - 图像分析相关属性
    @Published var analysisResult: ImageAnalysisResult? // 分析结果
    @Published var isAnalyzing = false // 是否正在分析
    @Published var analysisError: String? // 分析错误信息
    @Published var selectedAnalysisMethod: AnalysisMethod = .dify // 选择的分析方法

    enum AnalysisMethod: String, CaseIterable {
        case gemini = "Gemini"
        case dify = "Dify"

        var displayName: String {
            return self.rawValue
        }

        var icon: String {
            switch self {
            case .gemini:
                return "brain.head.profile"
            case .dify:
                return "cpu"
            }
        }
    }

    private let imageProcessingService = ImageProcess()
    private let geminiService = GeminiImageAnalysisService() // Gemini分析服务
    private let difyService = DifyImageAnalysisService() // Dify分析服务

    @MainActor
    func checkPhotoLibraryPermission() async -> Bool {
        let status = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        switch status {
        case .authorized, .limited:
            return true
        case .notDetermined:
            return await withCheckedContinuation { continuation in
                PHPhotoLibrary.requestAuthorization(for: .readWrite) { newStatus in
                    continuation.resume(returning: newStatus == .authorized || newStatus == .limited)
                }
            }
        case .denied, .restricted:
            showPermissionAlert = true
            return false
        @unknown default:
            return false
        }
    }

    @MainActor
    func loadImage() {
        Task {
            // 检查相册权限
            guard await checkPhotoLibraryPermission() else {
                print("相册权限未授予")
                return
            }

            guard let selectedPhoto = selectedPhoto,
                  let data = try? await selectedPhoto.loadTransferable(type: Data.self),
                  let uiImage = UIImage(data: data) else {
                print("无法加载图片数据")
                return
            }

            inputImage = uiImage

            // 根据卡片类型决定下一步
            if selectedCardType == .scenery {
                // 风景卡片：直接进入分析阶段
                processedImage = uiImage
                currentStep = .imageAnalysis
                Task {
                    await analyzeExtractedImage(uiImage)
                }
            } else {
                // 购物卡片：进入主体选择阶段
                detectSubjects(in: uiImage)
                currentStep = .subjectSelection
            }
        }
    }
    
    // MARK: 检测主体的方法
    /// 重新检测主体（用于图像编辑后）
    @MainActor
    func reDetectSubjects() {
        guard let inputImage = inputImage else {
            // 如果没有图像，清空主体相关数据
            subjectObservation = nil
            subjects = []
            showSubjectSelection = false
            return
        }
        
        // 检测主体
        detectSubjects(in: inputImage)
    }
    
    /// 检测主体的通用方法
    private func detectSubjects(in image: UIImage) {
        if let observation = imageProcessingService.detectSubjects(in: image) {
            subjectObservation = observation
            // 计算所有主体的中心位置
            subjects = imageProcessingService.calculateSubjectCenters(from: observation)
            showSubjectSelection = !subjects.isEmpty
        } else {
            subjectObservation = nil
            subjects = []
            showSubjectSelection = false
        }

        // 清空处理后的图像和选择索引
        processedImage = nil
        selectedSubjectIndices.removeAll()
    }

    // MARK: - 处理主体选择
    @MainActor
    func handleImageTap(at position: CGPoint) {
        // 查找最近的主体
        if let nearestSubject = imageProcessingService.findNearestSubject(at: position, in: subjects) {
            toggleSubjectSelection(nearestSubject.index)
        }
    }

    @MainActor
    func toggleSubjectSelection(_ index: Int) {
        if selectedSubjectIndices.contains(index) {
            selectedSubjectIndices.remove(index)
        } else {
            selectedSubjectIndices.insert(index)
        }
    }

    @MainActor
    func selectAllSubjects() {
        selectedSubjectIndices = Set(subjects.map { $0.index })
    }

    @MainActor
    func clearSelection() {
        selectedSubjectIndices.removeAll()
    }

    @MainActor
    func processImage() {
        guard let inputImage = inputImage else { return }
        isProcessing = true

        Task {
            var extractedImage: UIImage?

            if selectedSubjectIndices.isEmpty {
                // 如果没有选择主体，提取所有主体
                extractedImage = imageProcessingService.extractSubject(
                    from: inputImage,
                    croppedToInstancesExtent: true
                )
            } else {
                // 提取选中的主体
                extractedImage = imageProcessingService.extractMultipleSubjects(
                    from: inputImage,
                    selectedIndices: selectedSubjectIndices,
                    croppedToInstancesExtent: true
                )
            }

            processedImage = extractedImage
            isProcessing = false

            // 主体提取完成后的处理
            if let extractedImage = extractedImage {
                // 如果是购物卡片，提取主题色
                if selectedCardType == .shopping {
                    extractThemeColorForShoppingCard(extractedImage)
                }

                // 进入主体提取结果显示阶段，不自动分析
                currentStep = .extractionResult
            }
        }
    }

    // MARK: - 图像分析控制方法

    /// 开始图像分析
    @MainActor
    func startImageAnalysis() {
        guard let processedImage = processedImage else { return }

        // 进入图像分析阶段
        currentStep = .imageAnalysis

        // 根据选择的方法进行分析
        Task {
            await analyzeExtractedImage(processedImage)
        }
    }

    /// 还原到原始图像
    @MainActor
    func restoreToOriginalImage() {
        // 将处理后的图像还原为原始图像
        if let inputImage = inputImage {
            processedImage = inputImage
        }

        // 清除分析结果
        analysisResult = nil
        analysisError = nil

        // 根据卡片类型决定返回到哪个步骤
        if selectedCardType == .scenery {
            // 风景卡片返回到图像选择
            currentStep = .imageSelection
        } else {
            // 购物卡片返回到主体选择
            currentStep = .subjectSelection
            if let inputImage = inputImage {
                detectSubjects(in: inputImage)
            }
        }
    }

    // MARK: - 图像分析功能
    /// 分析提取后的图像内容
    /// - Parameter image: 提取后的图像
    @MainActor
    func analyzeExtractedImage(_ image: UIImage) async {
        isAnalyzing = true
        analysisError = nil

        var result: ImageAnalysisResult?

        switch selectedAnalysisMethod {
        case .gemini:
            result = await analyzeWithGemini(image)
        case .dify:
            result = await analyzeWithDify(image)
        }

        if let result = result {
            analysisResult = result
            print("图像分析完成: Description=\(result.Description), Title=\(result.Title), Eco_friendly=\(result.Eco_friendly), Pack_value=\(result.Pack_value)")
        } else {
            analysisError = "分析失败"
            print("图像分析失败: \(analysisError ?? "未知错误")")
        }

        isAnalyzing = false
    }

    /// 使用Gemini进行图像分析
    @MainActor
    private func analyzeWithGemini(_ image: UIImage) async -> ImageAnalysisResult? {
        guard let cardType = selectedCardType else {
            analysisError = "未选择卡片类型"
            return nil
        }

        // 获取用户设置的API key（如果有的话）
        let appSettings = AppSettings()
        let customApiKey = appSettings.geminiApiKey.isEmpty ? nil : appSettings.geminiApiKey

        // 调用Gemini API分析图像
        let result = await geminiService.analyzeImage(image, customApiKey: customApiKey, cardType: cardType)

        if result == nil {
            analysisError = geminiService.errorMessage ?? "Gemini分析失败"
        }

        return result
    }

    /// 使用Dify进行图像分析
    @MainActor
    private func analyzeWithDify(_ image: UIImage) async -> ImageAnalysisResult? {
        guard let cardType = selectedCardType else {
            analysisError = "未选择卡片类型"
            return nil
        }

        // 调用Dify API分析图像
        let result = await difyService.analyzeImage(image, cardType: cardType)

        if result == nil {
            analysisError = difyService.errorMessage ?? "Dify分析失败"
        }

        return result
    }

    // MARK: - 卡片类型选择相关方法

    /// 选择卡片类型
    /// - Parameter cardType: 选择的卡片类型
    @MainActor
    func selectCardType(_ cardType: CardType) {
        selectedCardType = cardType
        showCardTypeSelection = false

        // 进入图片选择步骤
        currentStep = .imageSelection
    }

    /// 重置卡片类型选择
    @MainActor
    func resetCardTypeSelection() {
        selectedCardType = nil
        showCardTypeSelection = true
        processedImage = nil
        analysisResult = nil
        extractedThemeColor = nil
        subjects = []
        selectedSubjectIndices = []
        showSubjectSelection = false
        currentStep = .cardTypeSelection
        inputImage = nil
    }

    /// 返回上一步
    @MainActor
    func goToPreviousStep() {
        switch currentStep {
        case .cardTypeSelection:
            // 已经是第一步，无法返回
            break
        case .imageSelection:
            // 返回卡片类型选择
            resetCardTypeSelection()
        case .subjectSelection:
            // 返回图片选择
            currentStep = .imageSelection
            processedImage = nil
            analysisResult = nil
            subjects = []
            selectedSubjectIndices = []
            showSubjectSelection = false
        case .extractionResult:
            // 返回到主体选择或图像选择
            if selectedCardType == .scenery {
                // 风景卡片返回到图片选择
                currentStep = .imageSelection
                processedImage = nil
            } else {
                // 购物卡片返回到主体选择
                currentStep = .subjectSelection
                processedImage = nil
                if let inputImage = inputImage {
                    detectSubjects(in: inputImage)
                }
            }
        case .imageAnalysis:
            // 返回到主体提取结果显示
            currentStep = .extractionResult
            analysisResult = nil
            analysisError = nil
        }
    }

    /// 为购物卡片提取主题色
    /// - Parameter image: 输入图片
    @MainActor
    private func extractThemeColorForShoppingCard(_ image: UIImage) {
        guard selectedCardType == .shopping else { return }

        Task {
            if let themeColor = imageProcessingService.extractThemeColor(from: image) {
                await MainActor.run {
                    extractedThemeColor = themeColor
                    print("✅ 提取主题色成功: #\(themeColor)")
                }
            } else {
                print("❌ 主题色提取失败")
            }
        }
    }

    @MainActor
    func saveImage() {
        guard let processedImage = processedImage else { return }
        imageProcessingService.saveImageToAlbum(processedImage) { success, error in
            if success {
                self.showSaveAlert = true
            }
        }
    }
}
