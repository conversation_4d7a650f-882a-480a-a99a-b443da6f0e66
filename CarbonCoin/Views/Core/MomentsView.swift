//
//  MomentsView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/12.
//

import SwiftUI

// MARK: - 朋友圈视图

/// 朋友圈视图，显示所有好友的公开日志
struct MomentsView: View {

    // MARK: - Properties

    /// 头像点击回调
    let onAvatarTap: ((String) -> Void)?

    // MARK: - Environment Objects

    @EnvironmentObject var appSettings: AppSettings

    // MARK: - State Properties

    @StateObject private var friendViewModel = FriendViewModel()
    @StateObject private var logViewModel = LogViewModel()
    @State private var friendsLogViewModel: FriendsLogViewModel?

    /// 是否显示日期选择器
    @State private var showDatePicker = false

    // MARK: - Initialization

    init(onAvatarTap: ((String) -> Void)? = nil) {
        self.onAvatarTap = onAvatarTap
    }

    // MARK: - Body

    var body: some View {
        NavigationStack {
            ZStack {
                // 背景
                Color.globalBackgroundGradient
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // 筛选区域
                    filterSection
                        .padding(.horizontal, Theme.Spacing.md)
                        .padding(.top, Theme.Spacing.sm)

                    // 日志列表
                    if let friendsLogVM = friendsLogViewModel {
                        momentsListView(friendsLogVM: friendsLogVM)
                    } else {
                        // 加载状态
                        loadingView
                    }
                }
            }
            .navigationTitle("朋友圈")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                setupViewModel()
                Task {
                    await loadFriendsLogs()
                }
            }
            .refreshable {
                await refreshLogs()
            }
        }
    }

    // MARK: - Private Methods

    /// 设置ViewModel
    private func setupViewModel() {
        if friendsLogViewModel == nil {
            friendsLogViewModel = FriendsLogViewModel(friendViewModel: friendViewModel)
        }
    }

    /// 加载朋友圈日志
    private func loadFriendsLogs() async {
        guard let friendsLogVM = friendsLogViewModel else { return }
        await friendsLogVM.fetchFriendsLogs(currentUserId: appSettings.userId)
    }

    /// 刷新日志
    private func refreshLogs() async {
        guard let friendsLogVM = friendsLogViewModel else { return }
        await friendsLogVM.refreshLogs(currentUserId: appSettings.userId)
    }
}

// MARK: - View Components

extension MomentsView {

    // MARK: - 筛选区域

    private var filterSection: some View {
        VStack(spacing: Theme.Spacing.sm) {
            HStack(spacing: Theme.Spacing.sm) {
                // 类型筛选按钮
                recordTypeFilterButtons

                Spacer()

                // 日期筛选按钮
                dateFilterButton
            }
        }
    }

    // MARK: - 记录类型筛选按钮

    private var recordTypeFilterButtons: some View {
        HStack(spacing: Theme.Spacing.xs) {
            // 全部按钮
            FilterButton(
                title: "全部",
                isSelected: friendsLogViewModel?.selectedRecordType == nil,
                action: {
                    friendsLogViewModel?.selectedRecordType = nil
                    Task {
                        await friendsLogViewModel?.applyFilters(currentUserId: appSettings.userId)
                    }
                }
            )

            // 各类型按钮
            ForEach(RecordType.allCases, id: \.self) { recordType in
                FilterButton(
                    title: recordType.displayName,
                    isSelected: friendsLogViewModel?.selectedRecordType == recordType,
                    action: {
                        friendsLogViewModel?.selectedRecordType = recordType
                        Task {
                            await friendsLogViewModel?.applyFilters(currentUserId: appSettings.userId)
                        }
                    }
                )
            }
        }
    }

    // MARK: - 日期筛选按钮

    private var dateFilterButton: some View {
        Button(action: {
            showDatePicker = true
        }) {
            HStack(spacing: Theme.Spacing.xs) {
                Image(systemName: "calendar")
                    .font(.caption)
                Text("日期")
                    .font(.captionBrand)
            }
            .foregroundColor(.textSecondary)
            .padding(.horizontal, Theme.Spacing.sm)
            .padding(.vertical, Theme.Spacing.xs)
            .background(Color.cardBackground.opacity(0.3))
            .cornerRadius(Theme.CornerRadius.sm)
        }
        .sheet(isPresented: $showDatePicker) {
            DateFilterSheet(
                startDate: Binding(
                    get: { friendsLogViewModel?.startDate },
                    set: { friendsLogViewModel?.startDate = $0 }
                ),
                endDate: Binding(
                    get: { friendsLogViewModel?.endDate },
                    set: { friendsLogViewModel?.endDate = $0 }
                ),
                onApply: {
                    Task {
                        await friendsLogViewModel?.applyFilters(currentUserId: appSettings.userId)
                    }
                },
                onClear: {
                    friendsLogViewModel?.clearFilters()
                }
            )
        }
    }

    // MARK: - 朋友圈日志列表

    private func momentsListView(friendsLogVM: FriendsLogViewModel) -> some View {
        Group {
            if friendsLogVM.isLoading {
                loadingView
            } else if friendsLogVM.dayGroups.isEmpty {
                emptyStateView
            } else {
                ScrollView {
                    LazyVStack(spacing: Theme.Spacing.lg) {
                        ForEach(friendsLogVM.dayGroups) { dayGroup in
                            VStack(alignment: .leading, spacing: Theme.Spacing.md) {
                                // 日期标题
                                HStack {
                                    Text(dayGroup.formattedDate)
                                        .font(.title3Brand)
                                        .foregroundColor(.textPrimary)

                                    Spacer()

                                    Text("\(dayGroup.items.count)条")
                                        .font(.captionBrand)
                                        .foregroundColor(.textSecondary)
                                }
                                .padding(.horizontal, Theme.Spacing.md)

                                // 日志列表
                                ForEach(dayGroup.items) { log in
                                    RecordPublicItem(
                                        log: log,
                                        logViewModel: logViewModel,
                                        currentUserId: appSettings.userId,
                                        displayMode: .public,
                                        onAvatarTap: onAvatarTap
                                    )
                                    .padding(.horizontal, Theme.Spacing.md)
                                }
                            }
                        }
                    }
                    .padding(.vertical, Theme.Spacing.md)
                    .padding(.bottom, Theme.Spacing.tab)
                }
                .scrollContentBackground(.hidden)
            }
        }
    }

    // MARK: - 加载状态视图

    private var loadingView: some View {
        VStack(spacing: Theme.Spacing.md) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(.accent)

            Text("正在加载朋友圈...")
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // MARK: - 空状态视图

    private var emptyStateView: some View {
        VStack(spacing: Theme.Spacing.lg) {
            Image(systemName: "person.2.circle")
                .font(.system(size: 60))
                .foregroundColor(.textSecondary)

            VStack(spacing: Theme.Spacing.sm) {
                Text("朋友圈空空如也")
                    .font(.title3Brand)
                    .foregroundColor(.textPrimary)

                Text("还没有好友分享公开日志\n快去添加好友吧")
                    .font(.bodyBrand)
                    .foregroundColor(.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.horizontal, Theme.Spacing.xl)
    }
}

// MARK: - 筛选按钮组件

/// 筛选按钮组件
struct FilterButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.captionBrand)
                .foregroundColor(isSelected ? .textPrimary : .textSecondary)
                .padding(.horizontal, Theme.Spacing.sm)
                .padding(.vertical, Theme.Spacing.xs)
                .background(
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.sm)
                        .fill(isSelected ? Color.accent : Color.cardBackground.opacity(0.3))
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 日期筛选弹窗

/// 日期筛选弹窗
struct DateFilterSheet: View {
    @Binding var startDate: Date?
    @Binding var endDate: Date?
    let onApply: () -> Void
    let onClear: () -> Void

    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationStack {
            VStack(spacing: Theme.Spacing.lg) {
                // 开始日期选择
                VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                    Text("开始日期")
                        .font(.bodyBrand)
                        .foregroundColor(.textPrimary)

                    DatePicker(
                        "开始日期",
                        selection: Binding(
                            get: { startDate ?? Date() },
                            set: { startDate = $0 }
                        ),
                        displayedComponents: .date
                    )
                    .datePickerStyle(.compact)
                }

                // 结束日期选择
                VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                    Text("结束日期")
                        .font(.bodyBrand)
                        .foregroundColor(.textPrimary)

                    DatePicker(
                        "结束日期",
                        selection: Binding(
                            get: { endDate ?? Date() },
                            set: { endDate = $0 }
                        ),
                        displayedComponents: .date
                    )
                    .datePickerStyle(.compact)
                }

                Spacer()

                // 按钮区域
                HStack(spacing: Theme.Spacing.md) {
                    Button("清除") {
                        startDate = nil
                        endDate = nil
                        onClear()
                        dismiss()
                    }
                    .buttonStyle(SecondaryButtonStyle())

                    Button("应用") {
                        onApply()
                        dismiss()
                    }
                    .buttonStyle(PrimaryButtonStyle())
                }
            }
            .padding(Theme.Spacing.lg)
            .navigationTitle("日期筛选")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden()
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
        .presentationDetents([.medium])
    }
}

// MARK: - Preview

#Preview {
    MomentsView()
        .environmentObject(AppSettings())
}
