//
//  ChatView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

/// 聊天视图 - 现在显示朋友圈功能
struct ChatView: View {

    // MARK: - Properties

    /// 导航路径管理
    @State private var navigationPath = NavigationPath()

    // MARK: - Body

    var body: some View {
        NavigationStack(path: $navigationPath) {
            // 直接显示朋友圈
            MomentsView(onAvatarTap: handleAvatarTap)
                .navigationTitle("朋友圈")
                .navigationBarTitleDisplayMode(.large)
                .toolbarBackground(.clear, for: .navigationBar)
                .toolbarColorScheme(.dark, for: .navigationBar)
                .navigationDestination(for: String.self) { userId in
                    // 跳转到个人主页
                    HomepageView(userId: userId)
                }
        }
    }

    // MARK: - Private Methods

    /// 处理头像点击事件
    private func handleAvatarTap(_ userId: String) {
        navigationPath.append(userId)
    }
}

// MARK: - Preview

#Preview {
    ChatView()
        .stableBackground()
}
